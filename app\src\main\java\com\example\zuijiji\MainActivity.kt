package com.example.zuijiji

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.graphics.RenderEffect
import androidx.compose.ui.graphics.Shader
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.foundation.Canvas
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.nativeCanvas
import kotlin.math.abs
import kotlin.math.sqrt
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.PI
import kotlin.math.pow
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji.ui.theme.ZuijijiTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ZuijijiTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showBlurDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层
        BackgroundLayer(
            backgroundImageUri = backgroundImageUri,
            imagePickerLauncher = imagePickerLauncher,
            onShowDialog = { showBlurDialog = true },
            dialogOffset = dialogOffset,
            showDialog = showBlurDialog
        )

        // 毛玻璃弹窗
        if (showBlurDialog) {
            RealTimeBlurDialog(
                backgroundImageUri = backgroundImageUri,
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it }
            )
        }
    }
}

@Composable
fun BackgroundLayer(
    backgroundImageUri: Uri?,
    imagePickerLauncher: androidx.activity.result.ActivityResultLauncher<String>,
    onShowDialog: () -> Unit,
    dialogOffset: Offset,
    showDialog: Boolean
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 控制按钮
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 左下角按钮
            Button(
                onClick = { imagePickerLauncher.launch("image/*") },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(24.dp)
            ) {
                Text("选择图片")
            }

            // 右下角按钮
            Button(
                onClick = onShowDialog,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(24.dp)
            ) {
                Text("透镜效果")
            }
        }
    }
}

@Composable
fun RealTimeBlurDialog(
    backgroundImageUri: Uri?,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 透镜效果的圆形弹窗
        // 外部容器，处理拖拽
        Box(
            modifier = Modifier
                .size(200.dp)  // 圆形透镜大小
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 透明边框层 - 10dp边框，2.2f放大效果
            TransparentBorderEffect(
                backgroundImageUri = backgroundImageUri,
                lensOffset = offset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                innerRadius = 100.dp,
                borderWidth = 10.dp
            )

            // 透镜折射效果层
            LensRefractionEffect(
                backgroundImageUri = backgroundImageUri,
                lensOffset = offset,
                screenWidthPx = screenWidthPx,
                screenHeightPx = screenHeightPx,
                lensRadius = 100.dp
            )

            // 透镜边缘折射效果
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .border(
                        width = 8.dp,
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.8f),
                                Color.White.copy(alpha = 0.4f),
                                Color.Cyan.copy(alpha = 0.3f),
                                Color.Blue.copy(alpha = 0.2f),
                                Color.Transparent
                            ),
                            radius = 140.dp.value
                        ),
                        shape = CircleShape
                    )
            )

            // 透镜表面反光效果
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.3f),
                                Color.Transparent,
                                Color.White.copy(alpha = 0.1f)
                            ),
                            center = Offset(0.3f, 0.3f),
                            radius = 200f
                        )
                    )
            )
        }
    }
}

@Composable
fun LensRefractionEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    lensRadius: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val lensRadiusPx = with(density) { lensRadius.toPx() }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(CircleShape)
    ) {
        // 使用变形的背景图片来模拟折射效果
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "透镜折射背景",
                modifier = Modifier
                    .graphicsLayer {
                        // 透镜放大效果
                        scaleX = 1.2f
                        scaleY = 1.2f

                        // 轻微的旋转来模拟光学扭曲
                        rotationZ = sin(lensOffset.x * 0.01f) * 0f

                        // 透镜的球面扭曲效果
                        cameraDistance = 8f
                        rotationX = sin(lensOffset.y * 0.005f) * 1.5f
                        rotationY = cos(lensOffset.x * 0.005f) * 1.5f
                    }
                    .blur(1.5.dp)
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 透镜的球面扭曲计算
                            val centerX = constraints.maxWidth / 2f
                            val centerY = constraints.maxHeight / 2f
                            val radius = lensRadiusPx

                            // 计算透镜中心相对于屏幕的位置
                            val baseLensOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2
                            val baseLensOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2

                            // 添加球面透镜的非线性扭曲
                            val distanceFromCenter = sqrt(lensOffset.x * lensOffset.x + lensOffset.y * lensOffset.y)
                            val distortionFactor = (1f - (distanceFromCenter / radius).coerceIn(0f, 1f)) * 0.3f

                            val sphericalDistortionX = (lensOffset.x * distortionFactor * 0.1f).roundToInt()
                            val sphericalDistortionY = (lensOffset.y * distortionFactor * 0.1f).roundToInt()

                            placeable.place(
                                baseLensOffsetX + sphericalDistortionX,
                                baseLensOffsetY + sphericalDistortionY
                            )
                        }
                    },
                contentScale = ContentScale.Crop
            )
        }

        // 添加透镜的色散和边缘效果
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height / 2

            // 绘制色散光谱效果 - 模拟光的色散
            for (angle in 0 until 360 step 5) {
                val radians = angle * PI / 180
                val innerRadius = lensRadiusPx * 0.85f
                val outerRadius = lensRadiusPx * 0.98f

                // 色散效果 - 不同颜色在不同半径
                val redRadius = innerRadius + (outerRadius - innerRadius) * 0.7f
                val greenRadius = innerRadius + (outerRadius - innerRadius) * 0.85f
                val blueRadius = innerRadius + (outerRadius - innerRadius) * 1.0f

                val baseX = centerX + cos(radians).toFloat()
                val baseY = centerY + sin(radians).toFloat()

                // 红色色散
                drawCircle(
                    color = Color.Red.copy(alpha = 0.15f),
                    radius = 1.5f,
                    center = Offset(baseX * redRadius / innerRadius, baseY * redRadius / innerRadius)
                )

                // 绿色色散
                drawCircle(
                    color = Color.Green.copy(alpha = 0.12f),
                    radius = 1.2f,
                    center = Offset(baseX * greenRadius / innerRadius, baseY * greenRadius / innerRadius)
                )

                // 蓝色色散
                drawCircle(
                    color = Color.Blue.copy(alpha = 0.1f),
                    radius = 1.0f,
                    center = Offset(baseX * blueRadius / innerRadius, baseY * blueRadius / innerRadius)
                )
            }

            // 透镜边缘的焦散线效果
            for (i in 0 until 12) {
                val angle = i * 30f * PI / 180
                val startRadius = lensRadiusPx * 0.6f
                val endRadius = lensRadiusPx * 0.9f

                val startX = centerX + cos(angle).toFloat() * startRadius
                val startY = centerY + sin(angle).toFloat() * startRadius
                val endX = centerX + cos(angle).toFloat() * endRadius
                val endY = centerY + sin(angle).toFloat() * endRadius

                drawLine(
                    color = Color.White.copy(alpha = 0.2f),
                    start = Offset(startX, startY),
                    end = Offset(endX, endY),
                    strokeWidth = 0.8f
                )
            }
        }
    }
}

@Composable
fun TransparentBorderEffect(
    backgroundImageUri: Uri?,
    lensOffset: Offset,
    screenWidthPx: Float,
    screenHeightPx: Float,
    innerRadius: androidx.compose.ui.unit.Dp,
    borderWidth: androidx.compose.ui.unit.Dp
) {
    val density = LocalDensity.current
    val innerRadiusPx = with(density) { innerRadius.toPx() }
    val outerRadiusPx = with(density) { (innerRadius + borderWidth).toPx() }

    // 透明边框区域 - 环形遮罩
    Box(
        modifier = Modifier
            .size((innerRadius + borderWidth) * 2)
            .align(Alignment.Center)
            .clip(CircleShape)
    ) {
        // 边框区域的高倍放大背景
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "透明边框放大效果",
                modifier = Modifier
                    .graphicsLayer {
                        // 边框区域的强烈放大效果
                        scaleX = 2.2f
                        scaleY = 2.2f

                        // 边框区域的扭曲效果
                        rotationZ = sin(lensOffset.x * 0.008f) * 5f
                        cameraDistance = 6f
                        rotationX = sin(lensOffset.y * 0.008f) * 8f
                        rotationY = cos(lensOffset.x * 0.008f) * 8f

                        // 透明度
                        alpha = 0.8f
                    }
                    .layout { measurable, constraints ->
                        val screenWidth = screenWidthPx.roundToInt()
                        val screenHeight = screenHeightPx.roundToInt()

                        val placeable = measurable.measure(
                            constraints.copy(
                                minWidth = screenWidth,
                                maxWidth = screenWidth,
                                minHeight = screenHeight,
                                maxHeight = screenHeight
                            )
                        )
                        layout(constraints.maxWidth, constraints.maxHeight) {
                            // 边框区域的扭曲偏移
                            val borderDistortionX = sin(lensOffset.y * 0.015f) * 8f
                            val borderDistortionY = cos(lensOffset.x * 0.015f) * 8f

                            val lensOffsetX = -lensOffset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2 + borderDistortionX.roundToInt()
                            val lensOffsetY = -lensOffset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2 + borderDistortionY.roundToInt()

                            placeable.place(lensOffsetX, lensOffsetY)
                        }
                    }
                    .blur(0.5.dp), // 轻微模糊增加真实感
                contentScale = ContentScale.Crop
            )
        }

        // 创建环形遮罩 - 只显示边框区域
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val centerX = size.width / 2
            val centerY = size.height / 2

            // 绘制外圆
            drawCircle(
                color = Color.Black,
                radius = outerRadiusPx,
                center = Offset(centerX, centerY)
            )

            // 挖空内圆，只保留边框环形区域
            drawCircle(
                color = Color.Transparent,
                radius = innerRadiusPx,
                center = Offset(centerX, centerY),
                blendMode = androidx.compose.ui.graphics.BlendMode.Clear
            )
        }

        // 边框的光学效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
                .border(
                    width = 1.dp,
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.4f),
                            Color.Cyan.copy(alpha = 0.2f),
                            Color.Blue.copy(alpha = 0.1f),
                            Color.Transparent
                        ),
                        radius = outerRadiusPx
                    ),
                    shape = CircleShape
                )
        )
    }
}